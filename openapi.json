{"openapi": "3.1.0", "info": {"title": "AI-Powered-Agri-Commerce API", "version": "1.0.0"}, "paths": {"/users/register": {"post": {"tags": ["Users"], "summary": "Register", "operationId": "register_users_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOut"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/users/login": {"post": {"tags": ["Users"], "summary": "<PERSON><PERSON>", "operationId": "login_users_login_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_login_users_login_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/users/me": {"get": {"tags": ["Users"], "summary": "Read Users Me", "operationId": "read_users_me_users_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOut"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/users/{user_id}": {"get": {"tags": ["Users"], "summary": "Get User", "operationId": "get_user_users__user_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOut"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Users"], "summary": "Delete User", "operationId": "delete_user_users__user_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/users/": {"get": {"tags": ["Users"], "summary": "List All Users", "operationId": "list_all_users_users__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserOut"}, "type": "array", "title": "Response List All Users Users  Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/users/broadcast": {"post": {"tags": ["Users"], "summary": "Broadcast Message", "operationId": "broadcast_message_users_broadcast_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/produce/": {"get": {"tags": ["Produce"], "summary": "List Produce", "operationId": "list_produce_produce__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ProduceOut"}, "type": "array", "title": "Response List Produce Produce  Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "post": {"tags": ["Produce"], "summary": "Create Produce", "operationId": "create_produce_produce__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProduceCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProduceOut"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/produce/{produce_id}": {"get": {"tags": ["Produce"], "summary": "Get Produce", "operationId": "get_produce_produce__produce_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "produce_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Produce Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/ProduceOut"}, {"type": "null"}], "title": "Response Get Produce Produce  Produce Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/broadcast/": {"get": {"tags": ["Broadcast"], "summary": "List Broadcasts", "operationId": "list_broadcasts_broadcast__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/BroadcastOut"}, "type": "array", "title": "Response List Broadcasts Broadcast  Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "post": {"tags": ["Broadcast"], "summary": "Send Broadcast", "operationId": "send_broadcast_broadcast__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastOut"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/files/upload": {"post": {"tags": ["Upload"], "summary": "Upload File", "operationId": "upload_file_files_upload_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_file_files_upload_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/negotiations/negotiations/": {"post": {"tags": ["Negotiation", "Negotiation"], "summary": "Post Offer", "operationId": "post_offer_negotiations_negotiations__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NegotiationCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NegotiationOut"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/negotiations/negotiations/{produce_id}": {"get": {"tags": ["Negotiation", "Negotiation"], "summary": "Get Offers", "operationId": "get_offers_negotiations_negotiations__produce_id__get", "parameters": [{"name": "produce_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Produce Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NegotiationOut"}, "title": "Response Get Offers Negotiations Negotiations  Produce Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/recommendations/recommendations/": {"get": {"tags": ["Recommendations", "Recommendation"], "summary": "Recommend Produce", "operationId": "recommend_produce_recommendations_recommendations__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "Limit"}}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "available", "title": "Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProduceOut"}, "title": "Response Recommend Produce Recommendations Recommendations  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/recommendations/recommendations/recommendations": {"get": {"tags": ["Recommendations", "Recommendation"], "summary": "Get Recommendations", "operationId": "get_recommendations_recommendations_recommendations_recommendations_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ProduceOut"}, "type": "array", "title": "Response Get Recommendations Recommendations Recommendations Recommendations Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/recommendations/recommendations/recommendations/collab": {"get": {"tags": ["Recommendations", "Recommendation"], "summary": "Get Collab Recommendations", "operationId": "get_collab_recommendations_recommendations_recommendations_recommendations_collab_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ProduceOut"}, "type": "array", "title": "Response Get Collab Recommendations Recommendations Recommendations Recommendations Collab Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/health": {"get": {"summary": "Health Check", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"Body_login_users_login_post": {"properties": {"grant_type": {"anyOf": [{"type": "string", "pattern": "^password$"}, {"type": "null"}], "title": "Grant Type"}, "username": {"type": "string", "title": "Username"}, "password": {"type": "string", "format": "password", "title": "Password"}, "scope": {"type": "string", "title": "<PERSON><PERSON>", "default": ""}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "format": "password", "title": "Client Secret"}}, "type": "object", "required": ["username", "password"], "title": "Body_login_users_login_post"}, "Body_upload_file_files_upload_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_file_files_upload_post"}, "BroadcastOut": {"properties": {"id": {"type": "integer", "title": "Id"}, "message": {"type": "string", "title": "Message"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "message", "created_at"], "title": "BroadcastOut"}, "BroadcastRequest": {"properties": {"message": {"type": "string", "title": "Message"}}, "type": "object", "required": ["message"], "title": "BroadcastRequest"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "NegotiationCreate": {"properties": {"produce_id": {"type": "integer", "title": "Produce Id"}, "message": {"type": "string", "title": "Message"}, "type": {"$ref": "#/components/schemas/NegotiationType"}}, "type": "object", "required": ["produce_id", "message", "type"], "title": "NegotiationCreate"}, "NegotiationOut": {"properties": {"id": {"type": "integer", "title": "Id"}, "produce_id": {"type": "integer", "title": "Produce Id"}, "sender_id": {"type": "integer", "title": "Sender Id"}, "sender_role": {"type": "string", "title": "Sender Role"}, "message": {"type": "string", "title": "Message"}, "type": {"$ref": "#/components/schemas/NegotiationType"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "produce_id", "sender_id", "sender_role", "message", "type", "created_at"], "title": "NegotiationOut"}, "NegotiationType": {"type": "string", "enum": ["propose", "accept", "decline"], "title": "NegotiationType"}, "ProduceCreate": {"properties": {"crop": {"type": "string", "title": "Crop"}, "category": {"type": "string", "title": "Category"}, "price_per_unit": {"type": "number", "title": "Price Per Unit"}, "unit": {"type": "string", "title": "Unit"}, "quantity": {"type": "number", "title": "Quantity"}, "grade": {"type": "string", "title": "Grade"}, "lat": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Lat"}, "lon": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Lon"}, "tags": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tags"}, "listing_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Listing Date"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "status": {"anyOf": [{"type": "string", "pattern": "^(available|sold|reserved)$"}, {"type": "null"}], "title": "Status", "default": "available"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}, "farmer_id": {"type": "integer", "title": "Farmer Id"}}, "type": "object", "required": ["crop", "category", "price_per_unit", "unit", "quantity", "grade", "farmer_id"], "title": "ProduceCreate"}, "ProduceOut": {"properties": {"crop": {"type": "string", "title": "Crop"}, "category": {"type": "string", "title": "Category"}, "price_per_unit": {"type": "number", "title": "Price Per Unit"}, "unit": {"type": "string", "title": "Unit"}, "quantity": {"type": "number", "title": "Quantity"}, "grade": {"type": "string", "title": "Grade"}, "lat": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Lat"}, "lon": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Lon"}, "tags": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tags"}, "listing_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Listing Date"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "status": {"anyOf": [{"type": "string", "pattern": "^(available|sold|reserved)$"}, {"type": "null"}], "title": "Status", "default": "available"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}, "id": {"type": "integer", "title": "Id"}, "farmer_id": {"type": "integer", "title": "Farmer Id"}}, "type": "object", "required": ["crop", "category", "price_per_unit", "unit", "quantity", "grade", "id", "farmer_id"], "title": "ProduceOut"}, "UserCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "role": {"type": "string", "pattern": "^(farmer|buyer)$", "title": "Role"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}, "phone_number": {"type": "string", "title": "Phone Number"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["name", "role", "phone_number", "password"], "title": "UserCreate"}, "UserOut": {"properties": {"name": {"type": "string", "title": "Name"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "role": {"type": "string", "pattern": "^(farmer|buyer)$", "title": "Role"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}, "phone_number": {"type": "string", "title": "Phone Number"}, "user_id": {"type": "integer", "title": "User Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["name", "role", "phone_number", "user_id", "created_at"], "title": "UserOut"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "/users/login"}}}}}}