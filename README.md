# AI-Powered Agri-Commerce Platform

An end-to-end FastAPI-based platform for digital agri-commerce that connects farmers and buyers, supports produce listing and negotiation, and delivers intelligent product recommendations using AI strategies.

---

## Features

- JWT-based User Authentication (Farmer/Buyer)
- Produce Listing & Categorization
- AI-Based Recommendation System:
  - Content-Based Filtering
  - Collaborative Filtering
  - Hybrid Strategy
- Negotiation System
- Broadcast Communication
- File Uploads for Certificates/Proof
- Feedback Collection & Ratings

---

## Tech Stack

| Layer         | Tech                             |
|---------------|----------------------------------|
| Backend       | FastAPI, SQLAlchemy              |
| Database      | SQLite (Dev), Postgres (Prod)    |
| Auth          | OAuth2 + JWT (via FastAPI)       |
| ORM Models    | SQLAlchemy                       |
| Frontend      | React.js (in separate repo)      |
| Deployment    | Uvicorn                          |

---

## Recommendation Service Architecture

This flow diagram explains how different strategies are handled within the `recommendation.py` service.

![Recommendation Service Flow](docs/recommendation_flow.png)

---

## 📁 Project Structure

```bash
backend/
├── app/
│   ├── crud/
│   ├── db/
│   │   ├── base_class.py
│   │   ├── database.py
│   │   ├── mockdata.py
│   │   ├── session.py
│   ├── models/
│   ├── routers/
│   ├── schemas/
│   ├── services/
│   ├── static/
│   ├── tests/
│   ├── config.py
│   └── main.py
├── .env
├── agri_db.db
├── init_db.sh
├── README.md
└── requirements.txt

backend/
├── app/
│   ├── main.py
│   ├── db/
│   │   ├── session.py
│   │   └── base_class.py
│   ├── models/
│   │   ├── user.py
│   │   ├── produce.py
│   │   ├── negotiation.py
│   │   └── feedback.py
│   ├── crud/
│   │   └── user.py
│   ├── services/
│   │   ├── auth.py
│   │   └── recommendation.py
│   ├── schemas/
│   │   ├── user.py
│   │   └── feedback.py
│   └── routers/
│       ├── user.py
│       ├── produce.py
│       ├── recommendation.py
│       └── feedback.py