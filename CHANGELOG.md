# CHANGELOG.md

## [v1.0.0] - 2025-06-22

### ✨ Added
- Implemented 10 diverse recommendation strategies:
  - Content-Based Filtering (`/recommendations/produce`)
  - General Recommendations (`/recommendations/general`)
  - Collaborative Filtering (`/recommendations/collaborative`)
  - Hybrid Recommendation Strategy (`/recommendations/hybrid`)
  - Trending Produce Based on Feedback Count (`/recommendations/trending`)
  - Feedback-Based Recommendations (`/recommendations/feedback`)
  - Negotiation-Based Recommendations (`/recommendations/negotiation`)
  - Preference-Based Recommendations (Placeholder) (`/recommendations/preferences`)
  - Location-Based Filtering (`/recommendations/location`)
  - Price Range-Based Filtering (`/recommendations/price`)

- Integrated fallback error handling in all service methods.
- Connected each strategy to corresponding FastAPI routes with query parameter support.
- Created visual RecSys strategy diagram for documentation and presentations.


### v1.0.0 (21.06.2025)
```bash
- Added functionlaity for sending broadcasting messages to all users (admin only)
- Added functionlaity for listing all broadcasts (All user)
- Added functionlaity for deleting broadcasts (admin only)
- Added functionlaity for updating broadcasts (admin only)
- Added testapi for LoginTest , CorsTest , TestRegister , BroadcastTest
  http://localhost:3001/test-register
  http://localhost:3001/cors-test
  http://localhost:3001/login-test
  http://localhost:3000/broadcast-test
  
- Schema change to add 'admin' role to table users (column: role)

 ### API Endpoints Available
- `GET /broadcast/` - List all broadcasts (all authenticated users)
- `POST /broadcast/` - Send broadcast (admin only)
- `GET /broadcast/{id}` - Get specific broadcast (all authenticated users)  
- `PUT /broadcast/{id}` - Update broadcast (admin only)
- `DELETE /broadcast/{id}` - Delete broadcast (admin only)
 
 ## Alternate Option: Update Database Constraint
Connect to your database and run:
-- Remove existing constraint
ALTER TABLE users DROP CONSTRAINT check_role_valid;

-- Add new constraint that includes admin
ALTER TABLE users ADD CONSTRAINT check_role_valid 
CHECK (role IN ('farmer', 'buyer', 'admin'));

```