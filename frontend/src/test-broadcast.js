// Simple test script to verify broadcast API functionality
import { broadcastAPI } from './utils/api.js';

// Test function to verify broadcast API
async function testBroadcastAPI() {
  console.log('Testing Broadcast API...');
  
  // Mock admin token (you'll need to replace this with a real admin token)
  const adminToken = 'your-admin-token-here';
  localStorage.setItem('token', adminToken);
  
  try {
    // Test sending a broadcast
    console.log('Sending test broadcast...');
    const result = await broadcastAPI.sendBroadcast('Test broadcast message');
    console.log('Broadcast sent successfully:', result);
    
    // Test getting broadcasts
    console.log('Fetching broadcasts...');
    const broadcasts = await broadcastAPI.getBroadcasts();
    console.log('Broadcasts retrieved:', broadcasts);
    
  } catch (error) {
    console.error('Error testing broadcast API:', error);
  }
}

// Uncomment to run the test
// testBroadcastAPI();

export default testBroadcastAPI;
