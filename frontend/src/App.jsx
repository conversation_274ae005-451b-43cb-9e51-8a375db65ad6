import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Register from "./pages/Register";
import Login from "./pages/Login";
import Home from "./pages/Home";
import Broadcast from "./pages/Broadcast";
import TestRegister from "./pages/TestRegister";
import CorsTest from "./pages/CorsTest";

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/register" element={<Register />} />
        <Route path="/login" element={<Login />} />
        <Route path="/broadcast" element={<Broadcast />} />
        <Route path="/test-register" element={<TestRegister />} />
        <Route path="/cors-test" element={<CorsTest />} />
      </Routes>
    </Router>
  );
}
export default App;
