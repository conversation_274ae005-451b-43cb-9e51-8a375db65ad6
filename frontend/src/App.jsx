import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Register from "./pages/Register";
import Login from "./pages/Login";
import Home from "./pages/Home";
import Broadcast from "./pages/Broadcast";
import RegisterProduce from "./pages/RegisterProduce";
import ProduceListing from "./pages/ProduceListing";
// Test APIs
import TestRegister from "./pages/TestRegister";
import CorsTest from "./pages/CorsTest";
import LoginTest from "./pages/LoginTest";
import BroadcastTest from "./pages/BroadcastTest";
import TestRegisterProduce from "./pages/TestRegisterProduce";
import TestProduceListing from "./pages/TestProduceListing";

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/register" element={<Register />} />
        <Route path="/login" element={<Login />} />
        <Route path="/broadcast" element={<Broadcast />} />
        <Route path="/registerproduce" element={<RegisterProduce />} />
        <Route path="/producelisting" element={<ProduceListing />} />
        <Route path="/test-register" element={<TestRegister />} />
        <Route path="/cors-test" element={<CorsTest />} />
        <Route path="/login-test" element={<LoginTest />} />
        <Route path="/broadcast-test" element={<BroadcastTest />} />
        <Route path="/test-RegisterProduce" element={<TestRegisterProduce />} />
        <Route path="/test-ProduceListing" element={<TestProduceListing />} />
      </Routes>
    </Router>
  );
}
export default App;
