import axios from "axios";

const API_BASE_URL = "http://localhost:8000";

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Broadcast API functions
export const broadcastAPI = {
  // Send a broadcast message (admin only)
  sendBroadcast: async (message) => {
    try {
      const response = await api.post("/broadcast/", { message });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get all broadcast messages (admin only)
  getBroadcasts: async () => {
    try {
      const response = await api.get("/broadcast/");
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Auth API functions
export const authAPI = {
  login: async (formData) => {
    const response = await axios.post(`${API_BASE_URL}/users/login`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    return response.data;
  },

  register: async (formData) => {
    console.log("API: Sending registration data:", formData);
    try {
      const response = await axios.post(`${API_BASE_URL}/users/register`, formData, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      console.log("API: Registration successful:", response.data);
      return response.data;
    } catch (error) {
      console.error("API: Registration failed:", error.response?.data || error.message);
      throw error;
    }
  },
};

export default api;
