.broadcast-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON>o', sans-serif;
}

.broadcast-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
}

.broadcast-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.broadcast-header p {
  color: #666;
  font-size: 16px;
}

/* Access Denied Styles */
.access-denied {
  text-align: center;
  padding: 40px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.access-denied h2 {
  color: #dc3545;
  margin-bottom: 15px;
}

.access-denied p {
  color: #6c757d;
  margin-bottom: 20px;
}

.access-denied a {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
}

.access-denied a:hover {
  text-decoration: underline;
}

/* Form Styles */
.broadcast-form-section {
  background-color: #f8f9fa;
  padding: 25px;
  border-radius: 8px;
  margin-bottom: 30px;
  border: 1px solid #dee2e6;
}

.broadcast-form-section h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 20px;
}

.broadcast-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.form-group textarea {
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
}

.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group textarea:disabled {
  background-color: #e9ecef;
  opacity: 0.6;
}

.broadcast-form button {
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  align-self: flex-start;
}

.broadcast-form button:hover:not(:disabled) {
  background-color: #0056b3;
}

.broadcast-form button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* Status Messages */
.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  margin-top: 10px;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #c3e6cb;
  margin-top: 10px;
}

/* Broadcasts List Styles */
.broadcasts-list-section {
  margin-bottom: 30px;
}

.broadcasts-list-section h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 20px;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

.no-broadcasts {
  text-align: center;
  padding: 40px;
  color: #6c757d;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.broadcasts-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.broadcast-item {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s;
}

.broadcast-item:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.broadcast-content {
  margin-bottom: 15px;
}

.broadcast-content p {
  margin: 0;
  color: #333;
  line-height: 1.5;
  font-size: 15px;
}

.broadcast-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
  font-size: 13px;
  color: #6c757d;
}

.broadcast-date {
  font-weight: 500;
}

.broadcast-id {
  font-family: monospace;
  background-color: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
}

/* Navigation */
.broadcast-navigation {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.nav-link {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
}

.nav-link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .broadcast-container {
    padding: 15px;
  }
  
  .broadcast-form-section {
    padding: 20px;
  }
  
  .broadcast-item {
    padding: 15px;
  }
  
  .broadcast-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
