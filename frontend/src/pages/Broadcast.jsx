import { useState, useEffect } from "react";
import { broadcastAPI } from "../utils/api";
import "./Broadcast.css";

export default function Broadcast() {
  const [message, setMessage] = useState("");
  const [broadcasts, setBroadcasts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Check if user is admin
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    // Check if user has admin role
    const token = localStorage.getItem("token");
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        setIsAdmin(payload.role === "admin");
      } catch (err) {
        console.error("Error parsing token:", err);
        setIsAdmin(false);
      }
    }

    // Load existing broadcasts if admin
    if (isAdmin) {
      loadBroadcasts();
    }
  }, [isAdmin]);

  const loadBroadcasts = async () => {
    try {
      setLoading(true);
      const data = await broadcastAPI.getBroadcasts();
      setBroadcasts(data);
    } catch (err) {
      setError("Failed to load broadcasts: " + (err.response?.data?.detail || err.message));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!message.trim()) {
      setError("Message cannot be empty");
      return;
    }

    try {
      setLoading(true);
      setError("");
      setSuccess("");
      
      await broadcastAPI.sendBroadcast(message.trim());
      setSuccess("Broadcast sent successfully!");
      setMessage("");
      
      // Reload broadcasts to show the new one
      await loadBroadcasts();
    } catch (err) {
      setError("Failed to send broadcast: " + (err.response?.data?.detail || err.message));
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  // If not admin, show access denied
  if (!isAdmin) {
    return (
      <div className="broadcast-container">
        <div className="access-denied">
          <h2>Access Denied</h2>
          <p>Only administrators can access the broadcast feature.</p>
          <a href="/">Go back to Home</a>
        </div>
      </div>
    );
  }

  return (
    <div className="broadcast-container">
      <div className="broadcast-header">
        <h1>Broadcast Messages</h1>
        <p>Send messages to all users in the system</p>
      </div>

      {/* Broadcast Form */}
      <div className="broadcast-form-section">
        <h2>Send New Broadcast</h2>
        <form onSubmit={handleSubmit} className="broadcast-form">
          <div className="form-group">
            <label htmlFor="message">Message:</label>
            <textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter your broadcast message here..."
              rows="4"
              required
              disabled={loading}
            />
          </div>
          
          <button type="submit" disabled={loading || !message.trim()}>
            {loading ? "Sending..." : "Send Broadcast"}
          </button>
        </form>

        {/* Status Messages */}
        {error && <div className="error-message">{error}</div>}
        {success && <div className="success-message">{success}</div>}
      </div>

      {/* Broadcasts List */}
      <div className="broadcasts-list-section">
        <h2>Previous Broadcasts</h2>
        
        {loading && <div className="loading">Loading broadcasts...</div>}
        
        {broadcasts.length === 0 && !loading ? (
          <div className="no-broadcasts">No broadcasts found.</div>
        ) : (
          <div className="broadcasts-list">
            {broadcasts.map((broadcast) => (
              <div key={broadcast.id} className="broadcast-item">
                <div className="broadcast-content">
                  <p>{broadcast.message}</p>
                </div>
                <div className="broadcast-meta">
                  <span className="broadcast-date">
                    {formatDate(broadcast.created_at)}
                  </span>
                  <span className="broadcast-id">ID: {broadcast.id}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Navigation */}
      <div className="broadcast-navigation">
        <a href="/" className="nav-link">← Back to Home</a>
      </div>
    </div>
  );
}
