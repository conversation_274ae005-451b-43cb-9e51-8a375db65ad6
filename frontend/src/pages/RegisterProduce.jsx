import React, { useState } from 'react';

const RegisterProduce = () => {
  const [form, setForm] = useState({
    crop: '',
    category: '',
    pricePerUnit: '',
    unit: '',
    quantity: '',
    grade: '',
    latitude: '',
    longitude: '',
    tags: '',
    description: '',
    imageUrl: ''
  });

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Submitted:', form);
    alert('Produce submitted successfully!');
    // Replace this with POST to backend
  };

  const handleEdit = () => {
    // Simulate fetching produce data for editing
    const sampleData = {
      crop: 'Wheat',
      category: 'Cereals',
      pricePerUnit: '₹20',
      unit: 'kg',
      quantity: '500',
      grade: 'Grade B',
      latitude: '28.6139',
      longitude: '77.2090',
      tags: 'Listing Date',
      description: 'High-quality wheat with excellent milling properties.',
      imageUrl: ''
    };
    setForm(sampleData);
    alert('Sample data loaded for editing.');
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to clear the form?')) {
      setForm({
        crop: '',
        category: '',
        pricePerUnit: '',
        unit: '',
        quantity: '',
        grade: '',
        latitude: '',
        longitude: '',
        tags: '',
        description: '',
        imageUrl: ''
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} style={styles.form}>
      <h2>Register Produce</h2>

      <label>Crop</label>
      <input name="crop" value={form.crop} onChange={handleChange} />

      <label>Category</label>
      <select name="category" value={form.category} onChange={handleChange}>
        <option value="">Select</option>
        <option value="Cereals">Cereals</option>
        <option value="Pulses">Pulses</option>
        <option value="Vegetables">Vegetables</option>
      </select>

      <div style={styles.row}>
        <div style={styles.half}>
          <label>Price per Unit</label>
          <input name="pricePerUnit" value={form.pricePerUnit} onChange={handleChange} />
        </div>
        <div style={styles.half}>
          <label>Unit</label>
          <input name="unit" value={form.unit} onChange={handleChange} />
        </div>
      </div>

      <div style={styles.row}>
        <div style={styles.half}>
          <label>Quantity</label>
          <input name="quantity" value={form.quantity} onChange={handleChange} />
        </div>
        <div style={styles.half}>
          <label>Grade</label>
          <select name="grade" value={form.grade} onChange={handleChange}>
            <option value="">Select</option>
            <option value="Grade A">Grade A</option>
            <option value="Grade B">Grade B</option>
            <option value="Grade C">Grade C</option>
          </select>
        </div>
      </div>

      <div style={styles.row}>
        <div style={styles.half}>
          <label>Latitude</label>
          <input name="latitude" value={form.latitude} onChange={handleChange} />
        </div>
        <div style={styles.half}>
          <label>Longitude</label>
          <input name="longitude" value={form.longitude} onChange={handleChange} />
        </div>
      </div>

      <label>Tags</label>
      <input name="tags" value={form.tags} onChange={handleChange} />

      <label>Description</label>
      <textarea name="description" value={form.description} onChange={handleChange} />

      <label>Image URL</label>
      <input name="imageUrl" value={form.imageUrl} onChange={handleChange} />

      <div style={styles.buttonRow}>
        <button type="submit" style={styles.button}>Submit</button>
        <button type="button" style={{ ...styles.button, backgroundColor: '#ccf' }} onClick={handleEdit}>Edit</button>
        <button type="button" style={{ ...styles.button, backgroundColor: '#fcc' }} onClick={handleDelete}>Delete</button>
      </div>
    </form>
  );
};

const styles = {
  form: {
    maxWidth: '500px',
    margin: '20px auto',
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
    padding: '20px',
    border: '1px solid #ccc',
    borderRadius: '10px',
    backgroundColor: '#f9f9f9'
  },
  row: {
    display: 'flex',
    gap: '10px'
  },
  half: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column'
  },
  buttonRow: {
    display: 'flex',
    justifyContent: 'space-between',
    gap: '10px',
    marginTop: '10px'
  },
  button: {
    padding: '10px',
    backgroundColor: '#eee',
    border: '1px solid #aaa',
    cursor: 'pointer',
    flex: 1
  }
};

export default RegisterProduce;
















































