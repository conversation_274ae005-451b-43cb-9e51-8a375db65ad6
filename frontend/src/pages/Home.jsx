import { useState, useEffect } from "react";

export default function Home() {
  const [userRole, setUserRole] = useState("");

  useEffect(() => {
    // Check user role from token
    const token = localStorage.getItem("token");
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        setUserRole(payload.role);
      } catch (err) {
        console.error("Error parsing token:", err);
      }
    }
  }, []);

  return (
    <div style={{ padding: "20px", textAlign: "center" }}>
      <h2>Welcome! You're logged in.</h2>

      {userRole && (
        <p>Your role: <strong>{userRole}</strong></p>
      )}

      <div style={{ marginTop: "30px" }}>
        <h3>Available Features:</h3>
        <div style={{ display: "flex", flexDirection: "column", gap: "10px", alignItems: "center" }}>
          {userRole === "admin" && (
            <a
              href="/broadcast"
              style={{
                padding: "10px 20px",
                backgroundColor: "#007bff",
                color: "white",
                textDecoration: "none",
                borderRadius: "5px",
                display: "inline-block"
              }}
            >
              📢 Broadcast Messages
            </a>
          )}

          <a
            href="/login"
            style={{
              padding: "10px 20px",
              backgroundColor: "#6c757d",
              color: "white",
              textDecoration: "none",
              borderRadius: "5px",
              display: "inline-block"
            }}
          >
            🔓 Logout
          </a>
        </div>
      </div>
    </div>
  );
}
