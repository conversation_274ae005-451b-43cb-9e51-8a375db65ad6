import { useState } from "react";
import axios from "axios";

export default function CorsTest() {
  const [result, setResult] = useState("");
  const [loading, setLoading] = useState(false);

  const testCors = async () => {
    setLoading(true);
    setResult("");

    try {
      // Test 1: Health check
      console.log("Testing health endpoint...");
      const healthResponse = await axios.get("http://localhost:8000/health");
      console.log("Health check successful:", healthResponse.data);

      // Test 2: Registration
      console.log("Testing registration...");
      const registrationData = {
        name: "CORS Frontend Test",
        email: "<EMAIL>",
        phone_number: "**********",
        password: "testpassword123",
        role: "farmer",
        location: "Frontend Test Location"
      };

      const regResponse = await axios.post(
        "http://localhost:8000/users/register",
        registrationData,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          withCredentials: true,
        }
      );

      console.log("Registration successful:", regResponse.data);

      setResult(`✅ CORS Test Successful!

Health Check: ${JSON.stringify(healthResponse.data, null, 2)}

Registration: ${JSON.stringify(regResponse.data, null, 2)}

Status: All CORS requests working properly!`);

    } catch (error) {
      console.error("CORS test failed:", error);
      
      let errorMessage = "❌ CORS Test Failed!\n\n";
      
      if (error.code === 'ERR_NETWORK') {
        errorMessage += "Network Error - Possible CORS issue or server not running\n";
      }
      
      if (error.response) {
        errorMessage += `Status: ${error.response.status}\n`;
        errorMessage += `Data: ${JSON.stringify(error.response.data, null, 2)}\n`;
        errorMessage += `Headers: ${JSON.stringify(error.response.headers, null, 2)}\n`;
      } else {
        errorMessage += `Error: ${error.message}\n`;
      }

      setResult(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <h2>CORS & API Test Page</h2>
      <p>This page tests if CORS is properly configured and the API is accessible from the frontend.</p>
      
      <button 
        onClick={testCors} 
        disabled={loading}
        style={{
          padding: "12px 24px",
          backgroundColor: loading ? "#6c757d" : "#007bff",
          color: "white",
          border: "none",
          borderRadius: "4px",
          cursor: loading ? "not-allowed" : "pointer",
          fontSize: "16px",
          fontWeight: "bold"
        }}
      >
        {loading ? "Testing CORS..." : "🧪 Test CORS & API"}
      </button>

      {result && (
        <div style={{ marginTop: "20px" }}>
          <h3>Test Results:</h3>
          <pre style={{ 
            backgroundColor: result.includes("✅") ? "#d4edda" : "#f8d7da",
            color: result.includes("✅") ? "#155724" : "#721c24",
            padding: "15px", 
            borderRadius: "4px",
            overflow: "auto",
            fontSize: "12px",
            border: `1px solid ${result.includes("✅") ? "#c3e6cb" : "#f5c6cb"}`,
            whiteSpace: "pre-wrap"
          }}>
            {result}
          </pre>
        </div>
      )}

      <div style={{ marginTop: "30px", padding: "15px", backgroundColor: "#f8f9fa", borderRadius: "4px" }}>
        <h3>Current Configuration:</h3>
        <ul style={{ margin: 0 }}>
          <li><strong>Frontend URL:</strong> {window.location.origin}</li>
          <li><strong>Backend URL:</strong> http://localhost:8000</li>
          <li><strong>CORS Enabled:</strong> Yes</li>
          <li><strong>Credentials:</strong> Included</li>
        </ul>
      </div>

      <div style={{ marginTop: "20px" }}>
        <a href="/">← Back to Home</a>
      </div>
    </div>
  );
}
