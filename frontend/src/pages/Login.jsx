import AuthForm from "../components/AuthForm";
import axios from "axios";

export default function Login() {
  const handleLogin = async (formData) => {
    try {
      const res = await axios.post("http://localhost:8000/login", formData);
      localStorage.setItem("token", res.data.access_token);
      alert("Login successful");
    } catch (err) {
      alert("Login failed: " + err.response?.data?.detail);
    }
  };
  return <AuthForm onSubmit={handleLogin} type="login" />;
}
