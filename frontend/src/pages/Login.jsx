import AuthForm from "../components/AuthForm";
import { authAPI } from "../utils/api";

export default function Login() {
  const handleLogin = async (formData) => {
    try {
      // Convert to form data format expected by backend
      const loginData = new URLSearchParams();
      loginData.append('username', formData.email); // Backend expects username field (phone_number)
      loginData.append('password', formData.password);

      const res = await authAPI.login(loginData);
      localStorage.setItem("token", res.access_token);
      alert("Login successful");

      // Redirect to home page
      window.location.href = "/";
    } catch (err) {
      alert("Login failed: " + (err.response?.data?.detail || err.message));
    }
  };
  return <AuthForm onSubmit={handleLogin} type="login" />;
}
