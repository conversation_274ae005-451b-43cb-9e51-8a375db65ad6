import AuthForm from "../components/AuthForm";
import { authAPI } from "../utils/api";

export default function Login() {
  const handleLogin = async (formData) => {
    try {
      console.log("Login form data received:", formData);

      // Validate required fields
      if (!formData.phone_number || !formData.password) {
        alert("Phone number and password are required");
        return;
      }

      // Convert to form data format expected by backend
      const loginData = new URLSearchParams();
      loginData.append('username', formData.phone_number); // Backend expects username field (phone_number)
      loginData.append('password', formData.password);

      console.log("Sending login data:", loginData.toString());

      const res = await authAPI.login(loginData);
      console.log("Login response:", res);

      localStorage.setItem("token", res.access_token);
      alert("Login successful");

      // Redirect to home page
      window.location.href = "/";
    } catch (err) {
      console.error("Login error:", err);

      let errorMessage = "Login failed";
      if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err.message) {
        errorMessage = err.message;
      }

      alert("Login failed: " + errorMessage);
    }
  };
  return <AuthForm onSubmit={handleLogin} type="login" />;
}
