import AuthForm from "../components/AuthForm";
import { authAPI } from "../utils/api";

export default function Register() {
  const handleRegister = async (formData) => {
    try {
      const res = await authAPI.register(formData);
      alert("Registration successful");

      // Redirect to login page
      window.location.href = "/login";
    } catch (err) {
      alert("Error: " + (err.response?.data?.detail || err.message));
    }
  };
  return <AuthForm onSubmit={handleRegister} type="register" />;
}
