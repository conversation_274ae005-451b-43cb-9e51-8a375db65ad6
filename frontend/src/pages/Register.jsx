import AuthForm from "../components/AuthForm";
import { authAPI } from "../utils/api";

export default function Register() {
  const handleRegister = async (formData) => {
    try {
      console.log("Submitting registration data:", formData);
      const res = await authAPI.register(formData);
      alert("Registration successful");

      // Redirect to login page
      window.location.href = "/login";
    } catch (err) {
      console.error("Registration error:", err);

      let errorMessage = "Registration failed";

      if (err.response) {
        // Server responded with error status
        if (err.response.data) {
          if (typeof err.response.data === 'string') {
            errorMessage = err.response.data;
          } else if (err.response.data.detail) {
            if (Array.isArray(err.response.data.detail)) {
              // Validation errors from Pydantic
              errorMessage = err.response.data.detail.map(e => `${e.loc.join('.')}: ${e.msg}`).join(', ');
            } else {
              errorMessage = err.response.data.detail;
            }
          } else {
            errorMessage = JSON.stringify(err.response.data);
          }
        }
      } else if (err.message) {
        errorMessage = err.message;
      }

      alert("Error: " + errorMessage);
    }
  };
  return <AuthForm onSubmit={handleRegister} type="register" />;
}
