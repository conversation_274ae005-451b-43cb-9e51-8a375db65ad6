import AuthForm from "../components/AuthForm";
import axios from "axios";

export default function Register() {
  const handleRegister = async (formData) => {
    try {
      const res = await axios.post("http://localhost:8000/register", formData);
      alert("Registration successful");
    } catch (err) {
      alert("Error: " + err.response?.data?.detail);
    }
  };
  return <AuthForm onSubmit={handleRegister} type="register" />;
}
