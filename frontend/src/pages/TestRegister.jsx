import { useState } from "react";
import { authAPI } from "../utils/api";

export default function TestRegister() {
  const [result, setResult] = useState("");
  const [loading, setLoading] = useState(false);

  const testRegistration = async () => {
    setLoading(true);
    setResult("");

    const testData = {
      name: "Test User",
      email: "<EMAIL>",
      phone_number: "1234567890",
      password: "password",
      role: "farmer",
      location: "Test Location"
    };

    try {
      console.log("Testing registration with data:", testData);
      const response = await authAPI.register(testData);
      setResult("SUCCESS: " + JSON.stringify(response, null, 2));
    } catch (error) {
      console.error("Registration test failed:", error);
      
      let errorDetails = {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers
      };
      
      setResult("ERROR: " + JSON.stringify(errorDetails, null, 2));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <h2>Registration Test Page</h2>
      
      <button 
        onClick={testRegistration} 
        disabled={loading}
        style={{
          padding: "10px 20px",
          backgroundColor: "#007bff",
          color: "white",
          border: "none",
          borderRadius: "4px",
          cursor: loading ? "not-allowed" : "pointer"
        }}
      >
        {loading ? "Testing..." : "Test Registration"}
      </button>

      {result && (
        <div style={{ marginTop: "20px" }}>
          <h3>Result:</h3>
          <pre style={{ 
            backgroundColor: "#f8f9fa", 
            padding: "15px", 
            borderRadius: "4px",
            overflow: "auto",
            fontSize: "12px"
          }}>
            {result}
          </pre>
        </div>
      )}

      <div style={{ marginTop: "30px" }}>
        <h3>Test Data Being Sent:</h3>
        <pre style={{ 
          backgroundColor: "#f8f9fa", 
          padding: "15px", 
          borderRadius: "4px",
          fontSize: "12px"
        }}>
{`{
  "name": "Test User",
  "email": "<EMAIL>", 
  "phone_number": "1234567890",
  "password": "testpassword123",
  "role": "farmer",
  "location": "Test Location"
}`}
        </pre>
      </div>

      <div style={{ marginTop: "20px" }}>
        <a href="/">← Back to Home</a>
      </div>
    </div>
  );
}
