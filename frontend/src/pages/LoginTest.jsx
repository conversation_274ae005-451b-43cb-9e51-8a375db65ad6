import { useState } from "react";
import axios from "axios";

export default function LoginTest() {
  const [result, setResult] = useState("");
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    phone_number: "9999999999",
    password: "testpassword123"
  });

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const testLogin = async () => {
    setLoading(true);
    setResult("");

    try {
      console.log("Testing login with data:", formData);

      // Test the exact format the backend expects
      const loginData = new URLSearchParams();
      loginData.append('username', formData.phone_number);
      loginData.append('password', formData.password);

      console.log("Sending login data:", loginData.toString());

      const response = await axios.post(
        "http://localhost:8000/users/login",
        loginData,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          withCredentials: true,
        }
      );

      console.log("Login successful:", response.data);

      // Store token
      localStorage.setItem("token", response.data.access_token);

      setResult(`✅ Login Test Successful!

Response: ${JSON.stringify(response.data, null, 2)}

Token stored in localStorage: ${response.data.access_token.substring(0, 50)}...

Status: Login working properly!`);

    } catch (error) {
      console.error("Login test failed:", error);
      
      let errorMessage = "❌ Login Test Failed!\n\n";
      
      if (error.code === 'ERR_NETWORK') {
        errorMessage += "Network Error - Possible CORS issue or server not running\n";
      }
      
      if (error.response) {
        errorMessage += `Status: ${error.response.status}\n`;
        errorMessage += `Status Text: ${error.response.statusText}\n`;
        errorMessage += `Data: ${JSON.stringify(error.response.data, null, 2)}\n`;
        errorMessage += `Headers: ${JSON.stringify(error.response.headers, null, 2)}\n`;
      } else {
        errorMessage += `Error: ${error.message}\n`;
      }

      setResult(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const testWithAPI = async () => {
    setLoading(true);
    setResult("");

    try {
      // Import and use the API utility
      const { authAPI } = await import('../utils/api');
      
      const loginData = new URLSearchParams();
      loginData.append('username', formData.phone_number);
      loginData.append('password', formData.password);

      const response = await authAPI.login(loginData);
      
      localStorage.setItem("token", response.access_token);

      setResult(`✅ API Login Test Successful!

Response: ${JSON.stringify(response, null, 2)}

Token stored: ${response.access_token.substring(0, 50)}...`);

    } catch (error) {
      console.error("API login test failed:", error);
      
      let errorMessage = "❌ API Login Test Failed!\n\n";
      
      if (error.response) {
        errorMessage += `Status: ${error.response.status}\n`;
        errorMessage += `Data: ${JSON.stringify(error.response.data, null, 2)}\n`;
      } else {
        errorMessage += `Error: ${error.message}\n`;
      }

      setResult(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <h2>Login Test Page</h2>
      <p>This page tests the login functionality with different approaches.</p>
      
      <div style={{ marginBottom: "20px", padding: "15px", backgroundColor: "#f8f9fa", borderRadius: "4px" }}>
        <h3>Test Credentials:</h3>
        <div style={{ display: "flex", flexDirection: "column", gap: "10px", maxWidth: "300px" }}>
          <input
            name="phone_number"
            placeholder="Phone Number"
            value={formData.phone_number}
            onChange={handleChange}
            style={{ padding: "8px", border: "1px solid #ccc", borderRadius: "4px" }}
          />
          <input
            name="password"
            type="password"
            placeholder="Password"
            value={formData.password}
            onChange={handleChange}
            style={{ padding: "8px", border: "1px solid #ccc", borderRadius: "4px" }}
          />
        </div>
      </div>

      <div style={{ display: "flex", gap: "10px", marginBottom: "20px" }}>
        <button 
          onClick={testLogin} 
          disabled={loading}
          style={{
            padding: "12px 24px",
            backgroundColor: loading ? "#6c757d" : "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: loading ? "not-allowed" : "pointer",
            fontSize: "16px"
          }}
        >
          {loading ? "Testing..." : "🧪 Test Direct Login"}
        </button>

        <button 
          onClick={testWithAPI} 
          disabled={loading}
          style={{
            padding: "12px 24px",
            backgroundColor: loading ? "#6c757d" : "#28a745",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: loading ? "not-allowed" : "pointer",
            fontSize: "16px"
          }}
        >
          {loading ? "Testing..." : "🔧 Test API Utility"}
        </button>
      </div>

      {result && (
        <div style={{ marginTop: "20px" }}>
          <h3>Test Results:</h3>
          <pre style={{ 
            backgroundColor: result.includes("✅") ? "#d4edda" : "#f8d7da",
            color: result.includes("✅") ? "#155724" : "#721c24",
            padding: "15px", 
            borderRadius: "4px",
            overflow: "auto",
            fontSize: "12px",
            border: `1px solid ${result.includes("✅") ? "#c3e6cb" : "#f5c6cb"}`,
            whiteSpace: "pre-wrap"
          }}>
            {result}
          </pre>
        </div>
      )}

      <div style={{ marginTop: "30px", padding: "15px", backgroundColor: "#f8f9fa", borderRadius: "4px" }}>
        <h3>Backend Requirements:</h3>
        <ul style={{ margin: 0 }}>
          <li><strong>Content-Type:</strong> application/x-www-form-urlencoded</li>
          <li><strong>Username field:</strong> phone_number (sent as 'username')</li>
          <li><strong>Password field:</strong> password</li>
          <li><strong>Response:</strong> access_token and token_type</li>
        </ul>
      </div>

      <div style={{ marginTop: "20px" }}>
        <a href="/">← Back to Home</a> | <a href="/login">Try Real Login</a>
      </div>
    </div>
  );
}
