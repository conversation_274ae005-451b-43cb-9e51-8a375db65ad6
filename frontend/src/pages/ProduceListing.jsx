// src/pages/ProduceListing.jsx
import React, { useState } from 'react';
import ProduceCard from '../components/ProduceCard';

const initialProduceList = [
  {
    id: 1,
    crop: 'Wheat',
    category: 'Cereals',
    price: 20,
    unit: 'kg',
    quantity: 500,
    description: 'High-quality wheat with excellent milling properties.',
  },
  {
    id: 2,
    crop: 'Wheat',
    category: 'Cereals',
    price: 20,
    unit: 'kg',
    quantity: 500,
    description: 'High-quality wheat with excellent milling properties.',
  },
];

const ProduceListing = () => {
  const [produceList, setProduceList] = useState(initialProduceList);

  const handleEdit = (produce) => {
    alert(`Edit clicked for: ${produce.crop}`);
    // You can replace this with a modal or form
  };

  const handleDelete = (produce) => {
    const confirm = window.confirm(`Are you sure you want to delete "${produce.crop}"?`);
    if (confirm) {
      setProduceList(produceList.filter(p => p.id !== produce.id));
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>Produce Listing</h2>
      {produceList.map((produce) => (
        <ProduceCard
          key={produce.id}
          produce={produce}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      ))}
    </div>
  );
};

export default ProduceListing;










































