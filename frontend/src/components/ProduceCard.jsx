import React from 'react';

const ProduceCard = ({ produce, onEdit, onDelete }) => {
  return (
    <div className="border p-4 flex gap-4 items-start rounded-md shadow-sm mb-4">
      <div className="w-24 h-24 bg-gray-200 flex items-center justify-center text-gray-600 rounded-md">
        Image
      </div>
      <div className="flex-grow">
        <h3 className="text-lg font-semibold">{produce.cropName}</h3>
        <p className="text-sm text-gray-500">{produce.category}</p>
        <p className="text-sm">
          ₹{produce.price} / kg • {produce.quantity} kg available
        </p>
        <p className="text-sm text-gray-700 mt-1">{produce.description}</p>
        <div className="flex gap-2 mt-2">
          <button
            onClick={() => onEdit(produce.id)}
            className="px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Edit
          </button>
          <button
            onClick={() => onDelete(produce.id)}
            className="px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProduceCard;



















