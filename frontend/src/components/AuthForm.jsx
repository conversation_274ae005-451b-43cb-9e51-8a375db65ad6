import { useState } from "react";

export default function AuthForm({ onSubmit, type }) {
  const [form, setForm] = useState({
    name: "",
    email: "",
    password: "",
    phone_number: "",
    role: "farmer",
    location: ""
  });

  const handleChange = e => setForm({ ...form, [e.target.name]: e.target.value });

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validation for registration
    if (type === "register") {
      if (!form.name.trim()) {
        alert("Name is required");
        return;
      }
      if (!form.phone_number.trim()) {
        alert("Phone number is required");
        return;
      }
      if (!form.password.trim()) {
        alert("Password is required");
        return;
      }
      if (!form.role) {
        alert("Role is required");
        return;
      }
    }

    // Validation for login
    if (type === "login") {
      if (!form.phone_number.trim()) {
        alert("Phone number is required");
        return;
      }
      if (!form.password.trim()) {
        alert("Password is required");
        return;
      }
    }

    console.log("Form submission data:", form);
    onSubmit(form);
  };

  return (
    <div style={{ maxWidth: "400px", margin: "0 auto", padding: "20px" }}>
      <h2>{type === "login" ? "Login" : "Register"}</h2>
      <form onSubmit={handleSubmit} style={{ display: "flex", flexDirection: "column", gap: "15px" }}>
        {type === "register" && (
          <>
            <input
              name="name"
              placeholder="Full Name"
              value={form.name}
              onChange={handleChange}
              required
              style={{ padding: "10px", border: "1px solid #ccc", borderRadius: "4px" }}
            />
            <input
              name="email"
              type="email"
              placeholder="Email"
              value={form.email}
              onChange={handleChange}
              required
              style={{ padding: "10px", border: "1px solid #ccc", borderRadius: "4px" }}
            />
            <input
              name="phone_number"
              placeholder="Phone Number"
              value={form.phone_number}
              onChange={handleChange}
              required
              style={{ padding: "10px", border: "1px solid #ccc", borderRadius: "4px" }}
            />
            <select
              name="role"
              value={form.role}
              onChange={handleChange}
              required
              style={{ padding: "10px", border: "1px solid #ccc", borderRadius: "4px" }}
            >
              <option value="farmer">Farmer</option>
              <option value="buyer">Buyer</option>
            </select>
            <input
              name="location"
              placeholder="Location (optional)"
              value={form.location}
              onChange={handleChange}
              style={{ padding: "10px", border: "1px solid #ccc", borderRadius: "4px" }}
            />
          </>
        )}

        {type === "login" && (
          <input
            name="phone_number"
            placeholder="Phone Number"
            value={form.phone_number}
            onChange={handleChange}
            required
            style={{ padding: "10px", border: "1px solid #ccc", borderRadius: "4px" }}
          />
        )}

        <input
          name="password"
          type="password"
          placeholder="Password"
          value={form.password}
          onChange={handleChange}
          required
          style={{ padding: "10px", border: "1px solid #ccc", borderRadius: "4px" }}
        />

        <button
          type="submit"
          style={{
            padding: "12px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            fontSize: "16px",
            cursor: "pointer"
          }}
        >
          {type === "login" ? "Login" : "Register"}
        </button>
      </form>

      <div style={{ textAlign: "center", marginTop: "20px" }}>
        {type === "login" ? (
          <p>Don't have an account? <a href="/register">Register here</a></p>
        ) : (
          <p>Already have an account? <a href="/login">Login here</a></p>
        )}
      </div>
    </div>
  );
}
