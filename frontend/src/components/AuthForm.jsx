import { useState } from "react";

export default function AuthForm({ onSubmit, type }) {
  const [form, setForm] = useState({ name: "", email: "", password: "" });

  const handleChange = e => setForm({ ...form, [e.target.name]: e.target.value });

  return (
    <form onSubmit={e => { e.preventDefault(); onSubmit(form); }}>
      {type === "register" && (
        <input name="name" placeholder="Name" onChange={handleChange} required />
      )}
      <input name="email" placeholder="Email" onChange={handleChange} required />
      <input name="password" type="password" placeholder="Password" onChange={handleChange} required />
      <button type="submit">{type === "login" ? "Login" : "Register"}</button>
    </form>
  );
}
