# Description: This code defines the API endpoints for user management, including registration, login, and profile retrieval.
# File: backend/app/routers/user.py

# import necessary libraries
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from app.schemas.user import UserCreate, UserLogin, UserOut, BroadcastRequest
from app.services import user as user_service
from app.services import auth as auth_service
from app.db.session import get_db
from jose import JWTError
from typing import Annotated
from typing import List
from pydantic import BaseModel

router = APIRouter()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/users/login")

# Function to define the API endpoint for user registration.
@router.post("/register", response_model=UserOut)
def register(user: UserCreate, db: Session = Depends(get_db)):

    # db_user = user_service.create_user(db, user)
    # if not db_user:
    #     raise HTTPException(status_code=400, detail="User with this phone or email already exists")
    # return db_user
    return {"message": "User created"}

# Function to define the API endpoint for user login.
@router.post("/login")
def login(form_data: Annotated[OAuth2PasswordRequestForm, Depends()], db: Session = Depends(get_db)):
    user = user_service.get_user_by_phone(db, form_data.username)
    if not user or not auth_service.verify_password(form_data.password, user.password_hash):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")

    token_data = {"sub": str(user.user_id), "role": user.role}
    token = auth_service.create_access_token(token_data)
    return {"access_token": token, "token_type": "bearer"}

# Function to define the API endpoint to get the current user's profile (JWT required)
@router.get("/me", response_model=UserOut)
def read_users_me(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    payload = auth_service.decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")
    user = user_service.get_user_by_id(db, int(payload.get("sub")))
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

# Function to define the API endpoint to get a user by their ID (JWT required)
@router.get("/{user_id}", response_model=UserOut)
def get_user(
    user_id: int,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    payload = auth_service.decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    # Optional: prevent users from accessing others' data (unless admin)
    if int(payload["sub"]) != user_id and payload.get("role") != "admin":
        raise HTTPException(status_code=403, detail="Access denied")

    user = user_service.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

# Function to define the API endpoint to list all users (JWT required)
@router.get("/", response_model=List[UserOut])
def list_all_users(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    # Decode JWT token
    payload = auth_service.decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    # Role-based access control (allow only buyer or admin)
    if payload.get("role") not in ["buyer", "admin"]:
        raise HTTPException(status_code=403, detail="Unauthorized access")

    # Return user list
    return user_service.list_users(db)

# Function to define the API endpoint to delete a user (admin only)
@router.delete("/{user_id}")
def delete_user(
    user_id: int,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    payload = auth_service.decode_access_token(token)
    if not payload or payload.get("role") != "admin":
        raise HTTPException(status_code=403, detail="Only admin can delete users")
    
    user = user_service.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user_service.delete_user(db, user_id)
    return {"detail": f"User {user_id} deleted"}

# Function to define the API endpoint to broadcast a message to all users (admin only)
@router.post("/broadcast")
def broadcast_message(
    req: BroadcastRequest,
    token: str = Depends(oauth2_scheme)
):
    payload = auth_service.decode_access_token(token)
    if not payload or payload.get("role") != "admin":
        raise HTTPException(status_code=403, detail="Only admin can broadcast")

    #  Replace this with actual email/notification logic later
    print(f"[Broadcast to all users] {req.message}")
    return {"message": "Broadcast sent"}