# Description: Broadcast management routes for sending and listing messages.
# File: backend/app/routers/broadcast.py

# import necessary libraries
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from fastapi.security import OAuth2PasswordBearer
from app.schemas.broadcast import BroadcastRequest, BroadcastOut
from app.services import auth as auth_service, broadcast as broadcast_service
from app.db.session import get_db
from typing import List

router = APIRouter()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/users/login")

# Function to send a broadcast message (admin only).
@router.post("/", response_model=BroadcastOut)
def send_broadcast(
    req: BroadcastRequest,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    payload = auth_service.decode_access_token(token)
    if not payload or payload.get("role") != "admin":
        raise HTTPException(status_code=403, detail="Only admin can broadcast")

    return broadcast_service.save_broadcast(db, req)

# Function to list all broadcast messages (admin only).
@router.get("/", response_model=List[BroadcastOut])
def list_broadcasts(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    payload = auth_service.decode_access_token(token)
    if not payload or payload.get("role") != "admin":
        raise HTTPException(status_code=403, detail="Only admin can view messages")

    return broadcast_service.list_broadcasts(db)