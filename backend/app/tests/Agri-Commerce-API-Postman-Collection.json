{"info": {"name": "Agri-Commerce API Collection", "_postman_id": "fda7a3c0-1234-4567-89ab-9f99999999aa", "description": "Postman collection for Agri-Commerce Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "User - <PERSON>gin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "url": {"raw": "http://localhost:8000/users/login", "host": ["http://localhost:8000"], "path": ["users", "login"]}, "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "9999999999"}, {"key": "password", "value": "test123"}]}}}, {"name": "Produce - List All", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "http://localhost:8000/produce", "host": ["http://localhost:8000"], "path": ["produce"]}}}, {"name": "Negotiation - Propose Deal", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "http://localhost:8000/negotiations", "host": ["http://localhost:8000"], "path": ["negotiations"]}, "body": {"mode": "raw", "raw": "{\"produce_id\": 1, \"message\": \"Propose \\u20b91000 for 10kg\", \"status\": \"propose\"}"}}}]}