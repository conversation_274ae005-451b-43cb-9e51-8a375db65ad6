# Description: Service functions for produce recommendations
# File: backend/app/services/recommendation.py

from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from typing import List, Optional
from fastapi import HTTPException
from app.models.produce import Produce
from app.models.negotiation import Negotiation
from app.models.feedback import Feedback
from app.schemas.produce import ProduceOut

# 1. Content-Based Filtering: Based on category, location, and status
def get_recommendations_for_buyer(
    db: Session,
    buyer_id: int,
    skip: int = 0,
    limit: int = 10,
    category: Optional[str] = None,
    location: Optional[str] = None,
    status: Optional[str] = "available"
) -> List[Produce]:
    filters = [Produce.status == status]
    if category:
        filters.append(Produce.category == category)
    if location:
        filters.append(Produce.location == location)

    results = (
        db.query(Produce)
        .filter(and_(*filters))
        .offset(skip)
        .limit(limit)
        .all()
    )
    if not results:
        raise HTTPException(status_code=404, detail="No produce found matching criteria.")
    return results

# 2. General Recommendations: e.g., top-rated or recently listed produce
def get_general_recommendations(db: Session, buyer_id: int) -> List[Produce]:
    results = (
        db.query(Produce)
        .filter(Produce.status == "available")
        .order_by(Produce.listing_date.desc())
        .limit(10)
        .all()
    )
    if not results:
        raise HTTPException(status_code=404, detail="No general recommendations available.")
    return results

# 3. Collaborative Filtering: Based on buyer's negotiation history
def get_collab_recommendations(db: Session, buyer_id: int) -> List[Produce]:
    negotiated_produce_ids = (
        db.query(Negotiation.produce_id)
        .filter(Negotiation.sender_id == buyer_id)
        .distinct()
        .all()
    )
    negotiated_ids = [pid[0] for pid in negotiated_produce_ids]

    if not negotiated_ids:
        return get_general_recommendations(db, buyer_id)

    results = (
        db.query(Produce)
        .filter(Produce.id.in_(negotiated_ids), Produce.status == "available")
        .limit(10)
        .all()
    )
    if not results:
        raise HTTPException(status_code=404, detail="No collaborative recommendations found.")
    return results

# 4. Hybrid Recommendations: Merge collaborative, content-based, and popular
def get_hybrid_recommendations(db: Session, buyer_id: int) -> List[Produce]:
    content = get_recommendations_for_buyer(db, buyer_id, limit=5)
    collab = get_collab_recommendations(db, buyer_id)
    general = get_general_recommendations(db, buyer_id)

    seen = set()
    combined = []

    def add_unique(produce_list):
        for item in produce_list:
            if item.id not in seen:
                seen.add(item.id)
                combined.append(item)

    add_unique(content)
    add_unique(collab)
    add_unique(general)

    if not combined:
        raise HTTPException(status_code=404, detail="No hybrid recommendations available.")

    return combined[:10]

# 5. Trending Produce: Based on feedback and negotiation activity
def get_trending_produce(db: Session, limit: int = 10) -> List[Produce]:
    trending = (
        db.query(Produce)
        .join(Feedback, Produce.id == Feedback.produce_id)
        .group_by(Produce.id)
        .order_by(func.count(Feedback.id).desc())
        .limit(limit)
        .all()
    )
    if not trending:
        raise HTTPException(status_code=404, detail="No trending produce found.")
    return trending

# 6. Recommendations Based on Feedback: Personalized based on buyer's feedback
def get_feedback_based_recommendations(db: Session, buyer_id: int, skip: int = 0, limit: int = 10) -> List[Produce]:
    feedbacks = (
        db.query(Feedback.produce_id)
        .filter(Feedback.user_id == buyer_id)
        .all()
    )
    produce_ids = [fb.produce_id for fb in feedbacks]

    if not produce_ids:
        return get_general_recommendations(db, buyer_id)

    results = (
        db.query(Produce)
        .filter(Produce.id.in_(produce_ids), Produce.status == "available")
        .offset(skip)
        .limit(limit)
        .all()
    )
    if not results:
        raise HTTPException(status_code=404, detail="No feedback-based recommendations found.")
    return results

# 7. Recommendations Based on Negotiation History: Personalized based on buyer's negotiation history
def get_negotiation_based_recommendations(db: Session, buyer_id: int, skip: int = 0, limit: int = 10) -> List[Produce]:
    negotiations = (
        db.query(Negotiation.produce_id)
        .filter(Negotiation.sender_id == buyer_id)
        .all()
    )
    produce_ids = [neg.produce_id for neg in negotiations]

    if not produce_ids:
        return get_general_recommendations(db, buyer_id)

    results = (
        db.query(Produce)
        .filter(Produce.id.in_(produce_ids), Produce.status == "available")
        .offset(skip)
        .limit(limit)
        .all()
    )
    if not results:
        raise HTTPException(status_code=404, detail="No negotiation-based recommendations found.")
    return results

# 8. Recommendations Based on User Preferences: Personalized based on buyer's preferences
def get_preferences_based_recommendations(db: Session, buyer_id: int, skip: int = 0, limit: int = 10) -> List[Produce]:
    # Assuming buyer preferences are stored in a separate table or model
    preferences = db.query(Produce).filter(Produce.status == "available").offset(skip).limit(limit).all()

    if not preferences:
        raise HTTPException(status_code=404, detail="No preferences-based recommendations found.")

    return preferences

# 9. Recommendations Based on Location: Personalized based on buyer's location
def get_location_based_recommendations(db: Session, buyer_id: int, location: str = None, skip: int = 0, limit: int = 10) -> List[Produce]:
    if not location:
        raise HTTPException(status_code=400, detail="Location must be provided for location-based recommendations.")

    results = (
        db.query(Produce)
        .filter(Produce.location == location, Produce.status == "available")
        .offset(skip)
        .limit(limit)
        .all()
    )
    if not results:
        raise HTTPException(status_code=404, detail="No location-based recommendations found.")
    return results

# 10. Recommendations Based on Price Range: Personalized based on buyer's price range
def get_price_based_recommendations(db: Session, buyer_id: int, min_price: float = 0.0, max_price: float = float('inf'), skip: int = 0, limit: int = 10) -> List[Produce]:
    results = (
        db.query(Produce)
        .filter(Produce.price_per_unit >= min_price, Produce.price_per_unit <= max_price, Produce.status == "available")
        .offset(skip)
        .limit(limit)
        .all()
    )
    if not results:
        raise HTTPException(status_code=404, detail="No price-based recommendations found.")
    return results