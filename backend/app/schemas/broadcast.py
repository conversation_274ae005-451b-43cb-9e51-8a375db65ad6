# Description: This module defines the schemas for broadcasting messages.
# File: backend/app/schemas/broadcast.py

# import necessary libraries
from pydantic import BaseModel
from datetime import datetime

class BroadcastRequest(BaseModel):
    message: str

class BroadcastOut(BaseModel):
    id: int
    message: str
    created_at: datetime

    class Config:
        from_attributes = True  # Pydantic v2 equivalent of `orm_mode = True`