# Description: SQLAlchemy model for broadcast messages
# File: backend/app/models/broadcast.py

# import necessary libraries
from sqlalchemy import Column, Integer, Text, DateTime
from sqlalchemy.sql import func
from app.db.session import Base

# Define class for SQLAlchemy model for broadcast messages
class Broadcast(Base):
    __tablename__ = "broadcasts"

    id = Column(Integer, primary_key=True, autoincrement=True)
    message = Column(Text, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())