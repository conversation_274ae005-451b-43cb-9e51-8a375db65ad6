# Description: This file is part of the AI-Powered-Agri-Commerce project. It initializes the FastAPI application, sets up the database, and includes routers for user and produce management.
# File: backend/app/main.py

# import necessary libraries
import os
from fastapi import FastAPI
from app.routers import user, produce, broadcast, negotiation, feedback, recommendation
from app.db.session import init_db
from fastapi.staticfiles import StaticFiles
from app.routers import upload
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="AI-Powered-Agri-Commerce API", version="1.0.0")

# Configure CORS origins based on environment
CORS_ORIGINS = [
    "http://localhost:3000",  # React default port
    "http://localhost:3001",  # Alternative React port
    "http://127.0.0.1:3000",
    "http://127.0.0.1:3001",
]

# Add production origins if specified
if os.getenv("FRONTEND_URL"):
    CORS_ORIGINS.append(os.getenv("FRONTEND_URL"))

app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
    ],
    expose_headers=["*"],
)

# Initialize database tables
init_db()

# Serve static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Register user router
app.include_router(user.router, prefix="/users", tags=["Users"])

# Register produce router
app.include_router(produce.router, prefix="/produce", tags=["Produce"])

# Register broadcast router
app.include_router(broadcast.router, prefix="/broadcast", tags=["Broadcast"])

# Register upload router
app.include_router(upload.router, prefix="/files", tags=["Upload"])

# Register negotiation router
app.include_router(negotiation.router, prefix="/negotiations", tags=["Negotiation"])   

# Register recommendation router
app.include_router(recommendation.router, prefix="/recommendations", tags=["Recommendations"])

# Register feedback router
app.include_router(feedback.router, prefix="/feedback", tags=["Feedback"])

# Health check endpoint
@app.get("/health")
def health_check():
    return {"status": "ok"}