# Description: <PERSON><PERSON><PERSON> to initialize the SQLite database for the FastAPI application.
# Usage: Run this script to set up the database before starting the FastAPI application.
# File: init_db.sh
# Format: Bash script

#!/bin/bash

# Activate virtual environment (if applicable)
if [ -f "venv/Scripts/activate" ]; then
  source venv/Scripts/activate
elif [ -f "venv/bin/activate" ]; then
  source venv/bin/activate
fi

# Set environment variables
export SQLALCHEMY_DATABASE_URL="sqlite:///./agri_app.db"

# Run Python DB initialization
echo "📦 Initializing SQLite DB using FastAPI models..."
python -c "
from app.db.database import Base, engine
from app.models import user, produce, negotiation
Base.metadata.create_all(bind=engine)
print('✅ Database initialized: agri_app.db')
"