# Description: Centralized config paths for ML pipeline
# File: backend/ml/config.py

# import libraries
import os

RAW_DATA_PATH = "ml/datasets/ml_training_data.csv"
ENHANCED_DATA_PATH = "ml/datasets/ml_training_data_enhanced.csv"
MODEL_PATH = "ml/models/latest_model.pkl"
LOG_PATH = "ml/logs/train_log.json"
LOG_HISTORY_PATH = "ml/logs/train_log_history.jsonl"
REPORT_PATH = "ml/reports/shap_summary.png"

def ensure_directories():
    os.makedirs(os.path.dirname(RAW_DATA_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(ENHANCED_DATA_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(LOG_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(LOG_HISTORY_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(REPORT_PATH), exist_ok=True)