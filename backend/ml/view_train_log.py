# Description: View training log history as a table and plot accuracy over time
# File: backend/view_train_log.py

import pandas as pd
import matplotlib.pyplot as plt
import json

LOG_HISTORY_PATH = "ml/logs/train_log_history.jsonl"

# Load training history
with open(LOG_HISTORY_PATH, "r") as f:
    records = [json.loads(line) for line in f.readlines()]

df = pd.DataFrame(records)
df['timestamp'] = pd.to_datetime(df['timestamp'])

# Display table
print("\n=== Training Log History ===")
print(df[['timestamp', 'accuracy', 'data_rows']].sort_values(by='timestamp', ascending=False))

# Plot accuracy trend
plt.figure(figsize=(10, 5))
plt.plot(df['timestamp'], df['accuracy'], marker='o')
plt.title("Model Accuracy Over Time")
plt.xlabel("Timestamp")
plt.ylabel("Accuracy")
plt.grid(True)
plt.tight_layout()
plt.show()