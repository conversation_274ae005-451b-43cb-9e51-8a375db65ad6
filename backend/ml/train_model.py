# Description: Train LightGBM model for produce recommendation scoring
# File: ml/train_model.py

import os
import sys
import json
from datetime import datetime

# Add backend directory to PYTHONPATH
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import lightgbm as lgb
import joblib
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
from config import RAW_DATA_PATH, ENHANCED_DATA_PATH, MODEL_PATH, LOG_PATH, LOG_HISTORY_PATH, ensure_directories

# Ensure directories exist
ensure_directories()

# Load data
data = pd.read_csv(RAW_DATA_PATH)

features = [
    'user_id', 'produce_id', 'price_per_unit', 'quantity',
    'category_encoded', 'location_encoded', 'avg_rating'
]
target = 'engaged'

X = data[features]
y = data[target]

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Train model
model = lgb.LGBMClassifier()
model.fit(X_train, y_train)

# Evaluate
preds = model.predict(X_test)
acc = accuracy_score(y_test, preds)
print(f"Model Accuracy: {acc:.2f}")

# Save model
joblib.dump(model, MODEL_PATH)
print(f"Model saved to {MODEL_PATH}")

# Log training metadata
log_data = {
    "timestamp": datetime.now().isoformat(),
    "accuracy": round(acc, 4),
    "features_used": features,
    "data_rows": len(data),
    "model_path": MODEL_PATH
}

# Overwrite current log
with open(LOG_PATH, "w") as f:
    json.dump(log_data, f, indent=2)

# Append to log history
with open(LOG_HISTORY_PATH, "a") as f:
    f.write(json.dumps(log_data) + "\n")

print(f"Training log saved to {LOG_PATH}")
print(f"Training history updated at {LOG_HISTORY_PATH}")
