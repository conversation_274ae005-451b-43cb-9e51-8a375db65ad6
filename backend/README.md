# Agri-Commerce Backend (FastAPI)

This is the backend API for an AI-powered agri-commerce platform connecting farmers and buyers.

---

## Features Implemented

- **User Auth & Roles**
  - JWT-based authentication
  - Role-based access (`farmer`, `buyer`, `admin`)
- **Produce Listings**
  - Farmers can post produce
  - Buyers can browse, search, and filter
- **Negotiation Engine**
  - Buyers can propose, accept, or decline deals
  - Unlimited back-and-forth messaging per produce
- **Feedback & Ratings**
  - After a successful deal, both parties can rate each other (1–5 stars) and leave comments
  - Feedback is tied to negotiations, with average ratings shown per produce
- **Recommendation System**
  - Suggests produce based on category, location, and user activity
- **Admin Tools**
  - Admin can delete users and broadcast messages
- **File Upload Support**
  - Upload images with max size limits, preview, and compression
- **Analytics**
  - API-level stats: total listings, produce by category, etc.

---

## Folder Structure

```bash
backend/
├── app/
│   ├── crud/
│   ├── db/
│   ├── models/
│   ├── routers/
│   ├── schemas/
│   ├── services/
│   ├── static/
│   ├── tests/
│   ├── config.py
│   └── main.py
├── .env
├── agri_db.db
├── init_db.sh
├── README.md
└── requirements.txt
```

---

## Setup Instructions

```bash
# 1. Clone the repo
git clone https://github.com/annam-ai-iitropar/team_5A.git
cd ai-powered-agri-commerce-app/backend

# 2. Change Directory
cd backend

# 3. Set up virtual environment ( Windows)
python -m venv venv
venv\Scripts\activate
 
# for(*nix) 
python3 -m venv venv
source venv/bin/activate 

# 4. Upgrade Pip and Install dependencies
pip install --upgrade pip
pip install -r requirements.txt

# 5. Run the server
uvicorn app.main:app --reload
```

---

## Authentication

- JWT Bearer token via `/users/login`
- Use `Authorization: Bearer <token>` for protected routes

---

## API Testing 

- API docs: [http://localhost:8000/docs](http://localhost:8000/docs)
- Postman collection: Provided below ⬇️